package com.sky.annotation;

import com.sky.enumeration.OperationType;

import java.lang.annotation.*;

/**
 * ClassName: AutoFill
 * Package: com.sky.annotation
 * Description:
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @dateTime 2025/7/20 16:04
 */

/**
 * 自动填充公共字段自定义注解
 */
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.METHOD)
public @interface AutoFill {

    /**
     * 默认为插入操作
     * @return
     */
    OperationType value() default OperationType.INSERT;

}
