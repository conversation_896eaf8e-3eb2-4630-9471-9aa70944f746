package com.sky.service;

/**
 * ClassName: CommonService
 * Package: com.sky.service
 * Description:
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @dateTime 2025/7/21 0:10
 */

import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;

/**
 * 通用公共接口
 */
public interface CommonService {

    /**
     * 上传文件到本地磁盘
     * @param file
     * @return
     * @throws IOException
     */
    String uploadFileToLocal(MultipartFile file) throws IOException;

    /**
     * 上传文件到阿里云OSS
     * @param file
     * @return
     */
    String uploadFileToAliYunOSS(MultipartFile file) throws IOException;
}
