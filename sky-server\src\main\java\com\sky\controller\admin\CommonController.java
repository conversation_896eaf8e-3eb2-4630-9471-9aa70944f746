package com.sky.controller.admin;

import com.sky.result.Result;
import com.sky.service.CommonService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;

/**
 * ClassName: CommonController
 * Package: com.sky.controller.admin
 * Description:
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @dateTime 2025/7/21 0:06
 */

/**
 * 通用公共接口
 */

@Api(tags = "通用公共接口")
@RestController
@RequestMapping("/admin/common")
@Slf4j
public class CommonController {

    @Autowired
    private CommonService commonService;

    /**
     * 上传文件到本地磁盘
     * @param file
     * @return
     * @throws IOException
     */
    @ApiOperation("上传文件到本地磁盘")
    @PostMapping("/uploadToLocal")
    public Result uploadFileToLocal(@RequestParam("file") MultipartFile file) throws IOException {
        log.info("上传文件到本地磁盘：{}", file != null ? file.getOriginalFilename() : "null");
        String url = commonService.uploadFileToLocal(file);
        log.info("文件上传本地磁盘成功，访问路径：{}", url);
        return Result.success(url);
    }

    /**
     * 上传文件到阿里云OSS
     * @param file
     * @return
     * @throws IOException
     */
    @ApiOperation("上传文件到阿里云OSS")
    @PostMapping("/upload")
    public Result uploadFileToAliYunOSS(@RequestParam("file") MultipartFile file) throws IOException {
        log.info("上传文件到阿里云OSS：{}", file != null ? file.getOriginalFilename() : "null");
        String url = commonService.uploadFileToAliYunOSS(file);
        log.info("文件上传阿里云OSS成功，访问路径：{}", url);
        return Result.success(url);
    }
}
