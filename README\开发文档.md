## 文件上传

### 本地存储

* 在服务端，接收到上传上来的文件之后，可以选择将文件存储在本地磁盘中。

```java
@RestController
public class UploadController {
    @PostMapping("/upload")
    public Result upload(MultipartFile image) throws IOException {
        // 获取原始文件名
        String originalFilename = image.getOriginalFilename();
        // 获取文件的扩展名
        String[] names = originalFilename.split("\\.");
        String extension = names[names.length - 1];
        // 构建新的文件名
        // String newFileName = UUID.randomUUID().toString() + "." + extension;
        String newFileName = UUID.randomUUID().toString() + originalFilename.substring(originalFilename.lastIndexOf("."));
        // 将文件保存在本地 E:/images/ 目录下
        image.transferTo(new File("E:/images/",newFileName));
        return Result.success();
    }
}
```

* 在配置文件（`application.yml`）中设置文件上传的大小限制

```yml
spring:
  servlet:
    multipart:
      max-file-size: 10MB	# 单个文件最大限制，这里设置为10MB（默认1MB）
      max-request-size: 100MB	# 总上传文件大小限制，这里设置为100MB
```

### 阿里云OSS对象存储服务

**`aliyun4723386436`**

[阿里云OSS的开通+配置及其使用_阿里云oss配置教程-CSDN博客](https://blog.csdn.net/AN_NI_112/article/details/132076550)

[OSS SDK快速入门_对象存储(OSS)-阿里云帮助中心](https://help.aliyun.com/zh/oss/getting-started/oss-sdk-quick-start)

[OSS Java SDK上传文件的多种方式_对象存储(OSS)-阿里云帮助中心](https://help.aliyun.com/zh/oss/developer-reference/overview-13/)

* **引入`OSS Java SDK`依赖**

  ```xml
  <!-- JDK1.8 -->
  <dependency>
      <groupId>com.aliyun.oss</groupId>
      <artifactId>aliyun-sdk-oss</artifactId>
      <version>3.17.4</version>
  </dependency>
  ```

* **上传文件流`Demo`**

  ```java
  import com.aliyun.oss.ClientException;
  import com.aliyun.oss.OSS;
  import com.aliyun.oss.common.auth.*;
  import com.aliyun.oss.OSSClientBuilder;
  import com.aliyun.oss.OSSException;
  import com.aliyun.oss.model.PutObjectRequest;
  import com.aliyun.oss.model.PutObjectResult;
  import java.io.FileInputStream;
  import java.io.InputStream;

  public class Demo {

      public static void main(String[] args) throws Exception {
          // Endpoint以华东1（杭州）为例，其它Region请按实际情况填写。
          String endpoint = "https://oss-cn-hangzhou.aliyuncs.com";
          // 从环境变量中获取访问凭证。运行本代码示例之前，请确保已设置环境变量OSS_ACCESS_KEY_ID和OSS_ACCESS_KEY_SECRET。
          EnvironmentVariableCredentialsProvider credentialsProvider = CredentialsProviderFactory.newEnvironmentVariableCredentialsProvider();
          // 填写Bucket名称，例如examplebucket。
          String bucketName = "examplebucket";
          // 填写Object完整路径，完整路径中不能包含Bucket名称，例如exampledir/exampleobject.txt。
          String objectName = "exampledir/exampleobject.txt";
          // 填写本地文件的完整路径，例如D:\\localpath\\examplefile.txt。
          // 如果未指定本地路径，则默认从示例程序所属项目对应本地路径中上传文件流。
          String filePath= "D:\\localpath\\examplefile.txt";
          // 填写Bucket所在地域。以华东1（杭州）为例，Region填写为cn-hangzhou。
          String region = "cn-hangzhou";

          // 创建OSSClient实例。
          ClientBuilderConfiguration clientBuilderConfiguration = new ClientBuilderConfiguration();
          clientBuilderConfiguration.setSignatureVersion(SignVersion.V4);
          OSS ossClient = OSSClientBuilder.create()
          .endpoint(endpoint)
          .credentialsProvider(credentialsProvider)
          .clientConfiguration(clientBuilderConfiguration)
          .region(region)
          .build();

          try {
              InputStream inputStream = new FileInputStream(filePath);
              // 创建PutObjectRequest对象。
              PutObjectRequest putObjectRequest = new PutObjectRequest(bucketName, objectName, inputStream);
              // 创建PutObject请求。
              PutObjectResult result = ossClient.putObject(putObjectRequest);
          } catch (OSSException oe) {
              System.out.println("Caught an OSSException, which means your request made it to OSS, "
                      + "but was rejected with an error response for some reason.");
              System.out.println("Error Message:" + oe.getErrorMessage());
              System.out.println("Error Code:" + oe.getErrorCode());
              System.out.println("Request ID:" + oe.getRequestId());
              System.out.println("Host ID:" + oe.getHostId());
          } catch (ClientException ce) {
              System.out.println("Caught an ClientException, which means the client encountered "
                      + "a serious internal problem while trying to communicate with OSS, "
                      + "such as not being able to access the network.");
              System.out.println("Error Message:" + ce.getMessage());
          } finally {
              if (ossClient != null) {
                  ossClient.shutdown();
              }
          }
      }
  }
  ```

####  **项目集成OSS**

* **引入`OSS Java SDK`依赖**

  ```xml
  <dependency>
      <groupId>com.aliyun.oss</groupId>
      <artifactId>aliyun-sdk-oss</artifactId>
      <version>3.17.4</version>
  </dependency>
  ```

* **配置文件OSS信息**

  ```yml
  #自定义的阿里云OSS配置信息
  aliyun.oss.bucketName=
  aliyun.oss.endpoint=
  aliyun.oss.accessKeyId=
  aliyun.oss.accessKeySecret=
  ```

* **阿里云OSS上传文件工具类**（由官方的示例代码改造而来）

  ```java
  import com.aliyun.oss.OSS;
  import com.aliyun.oss.OSSClientBuilder;
  import org.springframework.stereotype.Component;
  import org.springframework.web.multipart.MultipartFile;
  import org.springframework.beans.factory.annotation.Value;

  import java.io.IOException;
  import java.io.InputStream;
  import java.util.UUID;

  /**
   * 阿里云OSS上传文件工具类
  */
  @Component
  public class AliOSSUtils {
      @Value("${aliyun.oss.endpoint}")
      private String endpoint;

      @Value("${aliyun.oss.accessKeyId}")
      private String accessKeyId;

      @Value("${aliyun.oss.accessKeySecret}")
      private String accessKeySecret;

      @Value("${aliyun.oss.bucketName}")
      private String bucketName;

      /**
       * 实现上传图片到OSS
       */
      public String upload(MultipartFile multipartFile) throws IOException {
          // 获取上传的文件的输入流
          InputStream inputStream = multipartFile.getInputStream();

          // 避免文件覆盖
          String originalFilename = multipartFile.getOriginalFilename();
          String fileName = UUID.randomUUID().toString() + originalFilename.substring(originalFilename.lastIndexOf("."));

          //上传文件到 OSS
          OSS ossClient = new OSSClientBuilder().build(endpoint, accessKeyId, accessKeySecret);
          ossClient.putObject(bucketName, fileName, inputStream);

          //文件访问路径
          String url = endpoint.split("//")[0] + "//" + bucketName + "." + endpoint.split("//")[1] + "/" + fileName;

          // 关闭ossClient
          ossClient.shutdown();
          return url;// 把上传到oss的文件URL地址返回
      }
  }
  ```

* ##### `UploadController`代码

  ```java
  import com.itheima.pojo.Result;
  import com.itheima.utils.AliOSSUtils;
  import lombok.extern.slf4j.Slf4j;
  import org.springframework.beans.factory.annotation.Autowired;
  import org.springframework.web.bind.annotation.PostMapping;
  import org.springframework.web.bind.annotation.RestController;
  import org.springframework.web.multipart.MultipartFile;
  import java.io.IOException;

  @Slf4j
  @RestController
  public class UploadController {

      @Autowired
      private AliOSSUtils aliOSSUtils;

      @PostMapping("/upload")
      public Result upload(MultipartFile image) throws IOException {
          //调用阿里云OSS工具类，将上传上来的文件存入阿里云
          String url = aliOSSUtils.upload(image);
          //将图片上传完成后的url返回，用于浏览器前端回显展示
          return Result.success(url);
      }
  }
  ```





