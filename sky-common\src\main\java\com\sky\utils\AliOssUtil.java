package com.sky.utils;

import com.aliyun.oss.ClientException;
import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.aliyun.oss.OSSException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import java.io.ByteArrayInputStream;

//在 Spring 中，配置值绑定到类字段主要有两种方法，总结如下：
//1、@ConfigurationProperties 方式
    //作用：批量绑定配置文件中同一前缀的属性（如 aliyun.oss 下的所有子属性）。
    //依赖：需要类加 @Component（或通过配置类扫描），且字段需有 getter/setter（可通过 Lombok 的 @Data 自动生成）。
    //场景：适合配置项较多、有统一前缀的情况，支持松散绑定（如配置文件写 access-key-id 可绑定到 accessKeyId 字段）。
//2、@Value 方式
    //作用：单个绑定配置文件中的属性（如 @Value("${aliyun.oss.endpoint}")）。
    //依赖：无需 getter/setter，直接作用于字段，通过 Spring 容器注入。
    //场景：适合配置项较少的情况，支持 SpEL 表达式（如 @Value("#{systemProperties['user.name']}")）。

@Slf4j
@Component
public class AliOssUtil {

    /**
     * 阿里云服务器访问地址
     */
    @Value("${aliyun.oss.endpoint}")
    private String endpoint;
    /**
     * 阿里云账号的OSS密钥ID
     */
    @Value("${aliyun.oss.accessKeyId}")
    private String accessKeyId;
    /**
     * 阿里云账号的OSS访问密钥
     */
    @Value("${aliyun.oss.accessKeyId}")
    private String accessKeySecret;
    /**
     * 桶名 / 存放文件的文件夹名称
     */
    @Value("${aliyun.oss.bucketName}")
    private String bucketName;

    /**
     * 文件上传
     *
     * @param bytes 文件字节数组
     * @param objectName 文件名称
     * @return
     */
    public String upload(byte[] bytes, String objectName) {
        // 创建OSSClient实例。
        OSS ossClient = new OSSClientBuilder().build(endpoint, accessKeyId, accessKeySecret);
        try {
            // 创建PutObject请求。
            ossClient.putObject(bucketName, objectName, new ByteArrayInputStream(bytes));
        } catch (OSSException oe) {
            System.out.println("Caught an OSSException, which means your request made it to OSS, "
                    + "but was rejected with an error response for some reason.");
            System.out.println("Error Message:" + oe.getErrorMessage());
            System.out.println("Error Code:" + oe.getErrorCode());
            System.out.println("Request ID:" + oe.getRequestId());
            System.out.println("Host ID:" + oe.getHostId());
        } catch (ClientException ce) {
            System.out.println("Caught an ClientException, which means the client encountered "
                    + "a serious internal problem while trying to communicate with OSS, "
                    + "such as not being able to access the network.");
            System.out.println("Error Message:" + ce.getMessage());
        } finally {
            if (ossClient != null) {
                ossClient.shutdown();
            }
        }
        //文件访问路径规则 https://BucketName.Endpoint/ObjectName
        String processedEndpoint = endpoint.replace("https://", ""); // 移除前缀
        StringBuilder stringBuilder = new StringBuilder("https://");
        stringBuilder.append(bucketName).append(".").append(processedEndpoint).append("/").append(objectName);
        log.info("文件上传到:{}", stringBuilder);
        return stringBuilder.toString();
    }




//    /**
//     * 实现上传图片到OSS
//     */
//    public String upload(MultipartFile multipartFile) throws IOException {
//        // 获取上传的文件的输入流
//        InputStream inputStream = multipartFile.getInputStream();
//
//        // 避免文件覆盖
//        String originalFilename = multipartFile.getOriginalFilename();
//        String fileName = UUID.randomUUID().toString() + originalFilename.substring(originalFilename.lastIndexOf("."));
//
//        //上传文件到 OSS
//        OSS ossClient = new OSSClientBuilder().build(endpoint, accessKeyId, accessKeySecret);
//        ossClient.putObject(bucketName, fileName, inputStream);
//
//        //文件访问路径
//        String url = endpoint.split("//")[0] + "//" + bucketName + "." + endpoint.split("//")[1] + "/" + fileName;
//
//        // 关闭ossClient
//        ossClient.shutdown();
//        return url;// 把上传到oss的文件URL地址返回
//    }
}
