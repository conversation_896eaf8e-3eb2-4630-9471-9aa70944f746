package com.sky.config;

import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import com.sky.context.BaseContext;
import org.apache.ibatis.reflection.MetaObject;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

@Component
public class FillDataHandler implements MetaObjectHandler {
    /**
     * 定义插入数据时填充的方法，插入数据时相关数据会被封装到metaObject对象中,然后在对象中的数据插入到数据表中
     * @param metaObject
     */
    @Override
    public void insertFill(MetaObject metaObject) {
        LocalDateTime now = LocalDateTime.now(); // 每次调用时动态获取当前时间
        Long currentId = BaseContext.getCurrentId(); // 每次调用时动态获取当前用户ID
        // 设置createTime and createUser
        if (metaObject.hasSetter("createTime")) {
            metaObject.setValue("createTime", now);
        }
        if (metaObject.hasSetter("createUser")) {
            metaObject.setValue("createUser", currentId);
        }
        // 新增时也需要设置updateTime and updateUser
        if (metaObject.hasSetter("updateTime")) {
            metaObject.setValue("updateTime", now);
        }
        if (metaObject.hasSetter("updateUser")) {
            metaObject.setValue("updateUser", currentId);
        }
    }
    /**
     * 定义更新数据时填充的方法
     * @param metaObject
     */
    @Override
    public void updateFill(MetaObject metaObject) {
        LocalDateTime now = LocalDateTime.now(); // 每次调用时动态获取当前时间
        Long currentId = BaseContext.getCurrentId(); // 每次调用时动态获取当前用户ID
        if (metaObject.hasSetter("updateTime")) {
            metaObject.setValue("updateTime",now);
        }
        if (metaObject.hasSetter("updateUser")) {
            metaObject.setValue("updateUser", currentId);
        }
    }


//    /**
//     * // 自动校验：字段是否存在、类型是否匹配、是否需要填充（根据 @TableField(fill = ...)）
//     */
//    @Override
//    public void insertFill(MetaObject metaObject) {
//        this.strictInsertFill(metaObject, "createTime", LocalDateTime.class, LocalDateTime.now());
//        this.strictInsertFill(metaObject, "createUser", Long.class, BaseContext.getCurrentId());
//        this.strictInsertFill(metaObject, "updateTime", LocalDateTime.class, LocalDateTime.now());
//        this.strictInsertFill(metaObject, "updateUser", Long.class, BaseContext.getCurrentId());
//    }
//
//    @Override
//    public void updateFill(MetaObject metaObject) {
//        this.strictUpdateFill(metaObject, "updateTime", LocalDateTime.class, LocalDateTime.now());
//        this.strictUpdateFill(metaObject, "updateUser", Long.class, BaseContext.getCurrentId());
//    }




}
