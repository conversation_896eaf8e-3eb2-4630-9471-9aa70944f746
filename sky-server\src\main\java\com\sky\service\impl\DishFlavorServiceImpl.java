package com.sky.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sky.entity.DishFlavor;
import com.sky.mapper.DishFlavorMapper;
import com.sky.service.DishFlavorService;
import org.springframework.stereotype.Service;

/**
 * ClassName: DishFlavorServiceImpl
 * Package: com.sky.service.impl
 * Description:
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @dateTime 2025/8/2 11:24
 */

@Service
public class DishFlavorServiceImpl extends ServiceImpl<DishFlavorMapper, DishFlavor> implements DishFlavorService {
    /**
     * 保存菜品口味数据
     * @param dishFlavor
     */
    @Override
    public void saveDishFlavor(DishFlavor dishFlavor) {
        this.save(dishFlavor);
    }
}
