package com.sky.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sky.dto.EmployeeDTO;
import com.sky.dto.EmployeeLoginDTO;
import com.sky.dto.EmployeePageQueryDTO;
import com.sky.dto.PasswordEditDTO;
import com.sky.entity.Employee;
import com.sky.result.PageResult;

public interface EmployeeService extends IService<Employee>{

    /**
     * 员工登录
     * @param employeeLoginDTO
     * @return
     */
    Employee login(EmployeeLoginDTO employeeLoginDTO);

    void addEmployee(EmployeeDTO employeeDTO);

    PageResult queryPage(EmployeePageQueryDTO employeePageQueryDTO);

    /**
     * 启用禁用员工账号
     * @param status 状态：1启用，0禁用
     * @param id 员工ID
     */
    void startOrStop(Integer status, Long id);

    /**
     * 更新员工信息
     * @param employeeDTO
     * @return
     */
    Long updateEmployee(EmployeeDTO employeeDTO);

    /**
     * 根据id查询员工信息
     * @return
     */
    Employee getInfo(Long id);

    /**
     * 修改密码
     * @param passwordEditDTO
     * @return
     */
    Long updatePassword(PasswordEditDTO passwordEditDTO);
}
