package com.sky.controller.admin;

/**
 * ClassName: DishController
 * Package: com.sky.controller.admin
 * Description:
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @dateTime 2025/8/1 10:48
 */

import com.sky.annotation.AutoFill;
import com.sky.aspect.AutoFillAspect;
import com.sky.dto.DishDTO;
import com.sky.dto.DishPageQueryDTO;
import com.sky.entity.Dish;
import com.sky.enumeration.OperationType;
import com.sky.result.PageResult;
import com.sky.result.Result;
import com.sky.service.DishService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 菜品管理
 */
@Api(tags = "菜品管理")
@RestController
@RequestMapping("/admin/dish")
@Slf4j
public class DishController {

    @Autowired
    private DishService dishService;
    @Autowired
    private AutoFillAspect autoFillAspect;

    /**
     * 新增菜品
     * @param dishDTO
     * @return
     */
    @ApiOperation("新增菜品")
    @PostMapping
    public Result addDish(@RequestBody DishDTO dishDTO) {
        log.info("新增菜品信息: {}", dishDTO);
        dishService.addDish(dishDTO);
        return Result.success();
    }


    /**
     * 菜品列表分页查询
     * @param dishPageQueryDTO
     * @return
     */
    @ApiOperation("菜品列表分页查询")
    @GetMapping("/page")
    public Result list(DishPageQueryDTO dishPageQueryDTO){
        log.info("菜品列表分页查询参数：{}",dishPageQueryDTO);
        PageResult pageResult = dishService.list(dishPageQueryDTO);
        return Result.success(pageResult);
    }


}
