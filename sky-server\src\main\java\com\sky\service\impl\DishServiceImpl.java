package com.sky.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sky.annotation.AutoFill;
import com.sky.dto.DishDTO;
import com.sky.dto.DishPageQueryDTO;
import com.sky.entity.Dish;
import com.sky.entity.DishFlavor;
import com.sky.enumeration.OperationType;
import com.sky.mapper.DishMapper;
import com.sky.result.PageResult;
import com.sky.service.DishFlavorService;
import com.sky.service.DishService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * ClassName: DishServiceImpl
 * Package: com.sky.service.impl
 * Description:
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @dateTime 2025/8/2 11:06
 */

@Service
@Slf4j
public class DishServiceImpl extends ServiceImpl<DishMapper, Dish> implements DishService {

    @Autowired
    private DishFlavorService dishFlavorService;


    /**
     * 新增菜品
     * @param dishDTO
     */
    @Override
    public void addDish(DishDTO dishDTO) {
        // 保存菜品主数据
        Dish dish = new Dish();
        BeanUtils.copyProperties(dishDTO,dish);
        this.save(dish);
        // 保存菜品口味数据
        List<DishFlavor> flavors = dishDTO.getFlavors();
        Long id = dish.getId();
        flavors.forEach(flavor -> {
            flavor.setDishId(id);
            dishFlavorService.saveDishFlavor(flavor);
        });
        log.info("新增菜品数据：{},菜品口味数据：{}",dish,flavors);
    }

    /**
     * 菜品列表分页查询
     * @param dishPageQueryDTO
     * @return
     */
    @Override
    public PageResult list(DishPageQueryDTO dishPageQueryDTO) {
        // 构建分页参数
        IPage<Dish> page = new Page<>(dishPageQueryDTO.getPage(),dishPageQueryDTO.getPageSize());
        // 构建查询条件
        LambdaQueryWrapper<Dish> dishLambdaQueryWrapper = new LambdaQueryWrapper<Dish>()
                .eq(!StrUtil.isEmptyIfStr(dishPageQueryDTO.getCategoryId()),Dish::getCategoryId,dishPageQueryDTO.getCategoryId())
                .eq(!StrUtil.isEmptyIfStr(dishPageQueryDTO.getName()),Dish::getName,dishPageQueryDTO.getName())
                .eq(!StrUtil.isEmptyIfStr(dishPageQueryDTO.getStatus()),Dish::getStatus,dishPageQueryDTO.getStatus());
        // 查询分页列表数据
        this.page(page, dishLambdaQueryWrapper); // page 分页参数对象会自动接收并存储分页查询的结果数据
        log.info("菜品分页查询数据{}条：{}",page.getTotal(),page.getRecords());
        return new PageResult(page.getTotal(),page.getRecords());
    }
}
