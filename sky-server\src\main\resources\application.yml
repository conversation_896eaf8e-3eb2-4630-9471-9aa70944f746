﻿server:
  port: 8080

spring:
  profiles:
    active: dev
  main:
    allow-circular-references: true
  datasource:
    druid:
      driver-class-name: ${sky.datasource.driver-class-name}
      url: jdbc:mysql://${sky.datasource.host}:${sky.datasource.port}/${sky.datasource.database}?serverTimezone=Asia/Shanghai&useUnicode=true&characterEncoding=utf-8&zeroDateTimeBehavior=convertToNull&useSSL=false&allowPublicKeyRetrieval=true
      username: ${sky.datasource.username}
      password: ${sky.datasource.password}

mybatis:
  #mapper配置文件
  mapper-locations: classpath:mapper/*.xml
  type-aliases-package: com.sky.entity, com.sky.dto, com.sky.vo
  configuration:
    #开启驼峰命名
    map-underscore-to-camel-case: true
mybatis-plus:
  global-config:
    db-config:
      # 所有实体ID默认使用主键自增策略
      id-type: auto

# 日志级别：trace（追踪） < debug（调试） < info（信息） < warn（警告） < error（错误） < fatal（致命）
logging:
  level:
    com:
      sky:
        mapper: debug
        service: info
        controller: info

sky:
  jwt:
    # 设置jwt签名加密时使用的秘钥
    admin-secret-key: sky-take-out
    # 设置jwt过期时间(2 hour)
    #  admin-ttl: 7200000
    # 设置jwt过期时间(7 day)
    admin-ttl: *********
    # 设置前端传递过来的令牌名称
    admin-token-name: token
  account:
    default-password: "123456"

# TODO 配置阿里云OSS
aliyun:
  oss:
    accessKeyId: "LTAI5tPi3J6Jsvl6GBM8dy7P"
    accessKeySecret: "SQcXTzy25y9DRKdnq2LqueDWaABbLDd"
    endpoint: "https://oss-cn-hangzhou.aliyuncs.com"
    bucketName: "cangqiong-takeout"
