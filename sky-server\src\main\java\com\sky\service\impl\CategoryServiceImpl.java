package com.sky.service.impl;


import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sky.constant.StatusConstant;
import com.sky.context.BaseContext;
import com.sky.dto.CategoryDTO;
import com.sky.dto.CategoryPageQueryDTO;
import com.sky.entity.Category;
import com.sky.mapper.CategoryMapper;
import com.sky.result.PageResult;
import com.sky.service.CategoryService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@Slf4j
public class CategoryServiceImpl extends ServiceImpl<CategoryMapper, Category> implements CategoryService {

    @Autowired
    private CategoryMapper categoryMapper;

    @Override
    public void addCategory(CategoryDTO categoryDTO) {
        Category category = new Category();
        BeanUtils.copyProperties(categoryDTO, category);
        category.setStatus(StatusConstant.DISABLE); // 默认禁用
        category.setCreateUser(BaseContext.getCurrentId());
        category.setUpdateUser(BaseContext.getCurrentId());
        this.save(category);
        log.info("新增分类数据：{}", category);
    }

    @Override
    public PageResult queryPage(CategoryPageQueryDTO categoryPageQueryDTO) {
        // 构建查询条件
        LambdaQueryWrapper<Category> queryWrapper = new LambdaQueryWrapper<Category>()
                .like(StrUtil.isNotBlank(categoryPageQueryDTO.getName()), Category::getName, categoryPageQueryDTO.getName())
                .eq(categoryPageQueryDTO.getType() != null, Category::getType, categoryPageQueryDTO.getType());

        // 先查询总数
        long total = this.count(queryWrapper);

        // 再查询分页数据
        Page<Category> page = new Page<>(categoryPageQueryDTO.getPage(), categoryPageQueryDTO.getPageSize());
        this.page(page, queryWrapper);

        log.info("分类分页查询数据{}条：{}",total, page.getRecords());
        return new PageResult(total, page.getRecords());
    }


    @Override
    public Long updateCategory(CategoryDTO categoryDTO) {
        Category category = this.getById(categoryDTO.getId());
        BeanUtils.copyProperties(categoryDTO, category);
        category.setUpdateUser(BaseContext.getCurrentId());
        this.updateById(category);
        log.info("修改分类数据：{}", category);
        return category.getId();
    }

    @Override
    public void updateStatus(Integer status, Long id) {
        Category category = Category.builder().status(status).id(id).updateUser(BaseContext.getCurrentId()).build();
        this.updateById(category);
        log.info("启用禁用分类：{}",category);
    }

    @Override
    public void deleteCategory(Long id) {
        this.remove(new LambdaQueryWrapper<Category>().eq(Category::getId,id));
    }

    @Override
    public List<Category> selectListByType(Integer type) {
        return this.list(new LambdaQueryWrapper<Category>().eq(Category::getType,type));
    }


}
