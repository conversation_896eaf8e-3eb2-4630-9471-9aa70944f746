package com.sky.service;

/**
 * ClassName: DishService
 * Package: com.sky.service
 * Description:
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @dateTime 2025/8/2 11:03
 */

import com.baomidou.mybatisplus.extension.service.IService;
import com.sky.annotation.AutoFill;
import com.sky.dto.DishDTO;
import com.sky.dto.DishPageQueryDTO;
import com.sky.entity.Dish;
import com.sky.enumeration.OperationType;
import com.sky.result.PageResult;
import lombok.Value;

/**
 * 菜品管理接口
 */
public interface DishService extends IService<Dish> {

    /**
     * 新增菜品
     * @param dishDTO
     */
    void addDish(DishDTO dishDTO);

    /**
     * 菜品列表分页查询
     * @param dishPageQueryDTO
     * @return
     */
    PageResult list(DishPageQueryDTO dishPageQueryDTO);
}
