package com.sky.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.sky.dto.CategoryDTO;
import com.sky.dto.CategoryPageQueryDTO;
import com.sky.entity.Category;
import com.sky.result.PageResult;

import java.util.List;


/**
 * 分类管理
 */
public interface CategoryService extends IService<Category>{

    /**
     * 新增分类
     * @param categoryDTO
     */
    void addCategory(CategoryDTO categoryDTO);

    /**
     * 分类分页查询
     * @param categoryPageQueryDTO
     * @return
     */
    PageResult queryPage(CategoryPageQueryDTO categoryPageQueryDTO);

    /**
     * 修改分类
     * @return
     */
    Long updateCategory(CategoryDTO categoryDTO);

    /**
     * 启用禁用分类
     */
    void updateStatus(Integer status, Long id);

    /**
     * 根据id删除分类
     */
    void deleteCategory(Long id);

    /**
     * 根据类型查询分类
     * @param type
     * @return
     */
    List<Category> selectListByType(Integer type);
}
