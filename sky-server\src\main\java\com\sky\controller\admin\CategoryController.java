package com.sky.controller.admin;


import com.alibaba.druid.sql.PagerUtils;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sky.dto.CategoryDTO;
import com.sky.dto.CategoryPageQueryDTO;
import com.sky.entity.Category;
import com.sky.result.PageResult;
import com.sky.result.Result;
import com.sky.service.CategoryService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * 分类管理
 */
@Api(tags = "分类管理")
@RestController
@RequestMapping("/admin/category")
@Slf4j
public class CategoryController{

    @Autowired
    private CategoryService categoryService;


    /**
     * 新增分类
     * @param categoryDTO
     * @return
     */
    @ApiOperation("新增分类")
    @PostMapping
    public Result addCategory(@RequestBody CategoryDTO categoryDTO){
        log.info("新增分类信息：{}",categoryDTO);
        categoryService.addCategory(categoryDTO);
        return Result.success();
    }

    /**
     * 分类分页查询
     * @param categoryPageQueryDTO
     * @return
     */
    @ApiOperation("分类分页查询")
    @GetMapping("/page")
    public Result list(CategoryPageQueryDTO categoryPageQueryDTO){
        log.info("分类分页查询参数信息：{}",categoryPageQueryDTO);
        PageResult pageResult = categoryService.queryPage(categoryPageQueryDTO);
        return Result.success(pageResult);
    }

    /**
     * 修改分类
     * @param categoryDTO
     * @return
     */
    @ApiOperation("修改分类")
    @PutMapping
    public Result updateCategory(@RequestBody CategoryDTO categoryDTO){
        log.info("修改分类信息：{}",categoryDTO);
        Long id = categoryService.updateCategory(categoryDTO);
        return Result.success(id);
    }


    /**
     * 启用/禁用分类
     * @param status
     * @param id
     * @return
     */
    @ApiOperation("启用/禁用分类")
    @PostMapping("/status/{status}")
    public Result updateStatus(@PathVariable("status") Integer status , @RequestParam("id") Long id){
        log.info("启用禁用分类：id={}, status={}", id, status);
        categoryService.updateStatus(status,id);
        return Result.success();
    }

    /**
     * 根据id删除分类
     * @param id
     * @return
     */
    @ApiOperation("根据id删除分类")
    @DeleteMapping
    public Result deleteCategory(@RequestParam("id") Long id){
        log.info("删除id为：{}的分类",id);
        categoryService.deleteCategory(id);
        return Result.success();
    }

    /**
     * 根据类型查询分类
     * @param type
     * @return
     */
    @ApiOperation("根据类型查询分类")
    @GetMapping("/list")
    public Result selectList(@RequestParam("type") Integer type){
        List<Category> categoryList = categoryService.selectListByType(type);
        return Result.success(categoryList);
    }

}
