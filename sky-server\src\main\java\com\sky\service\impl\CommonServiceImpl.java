package com.sky.service.impl;

import com.sky.service.CommonService;
import com.sky.utils.AliOssUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.util.UUID;

/**
 * ClassName: CommonServiceImpl
 * Package: com.sky.service.impl
 * Description:
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @dateTime 2025/7/21 0:11
 */

/**
 * 通用功能接口实现类
 */
@Slf4j
@Service
public class CommonServiceImpl implements CommonService {

    @Autowired
    private AliOssUtil aliOssUtil;

    @Override
    public String uploadFileToLocal(MultipartFile multipartFile) throws IOException {
        // 获取上传的文件名
        String originalFilename = multipartFile.getOriginalFilename();
        // 提取文件扩展名
        String[] split = originalFilename.split("\\.");
        String extension = split[split.length - 1];
        // 构建存储到本地磁盘的文件名
        String newFilename = UUID.randomUUID() + "." + extension;

        // lastIndexOf(".")：找出原文件名里最后一个点（.）的索引位置。
        // substring(...)：从该索引位置开始，一直到字符串的末尾，提取出子字符串，这个子字符串就是文件的扩展名。
//        String newFilename = UUID.randomUUID() + originalFilename.substring(originalFilename.lastIndexOf("."));

        // 将上传的文件转存到本地磁盘
        File file = new File("C:/Users/<USER>'f/Downloads/", newFilename);
        multipartFile.transferTo(file);
        // 返回文件地址
        log.info("上传文件的本地存储地址为：{}{}{}",file.getPath(),file.getAbsoluteFile(),file.getCanonicalFile());
        return file.getPath();
    }

    /**
     * 上传文件到阿里云OSS
     * @param file
     * @return 上传文件的URL地址
     */
    @Override
    public String uploadFileToAliYunOSS(MultipartFile file) throws IOException {
        return aliOssUtil.upload(file.getBytes(), file.getOriginalFilename());
    }
}
