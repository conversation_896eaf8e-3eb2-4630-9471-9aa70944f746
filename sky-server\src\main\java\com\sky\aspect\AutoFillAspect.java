package com.sky.aspect;

/**
 * ClassName: AutoFillAspect
 * Package: com.sky.aspect
 * Description:
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @dateTime 2025/7/20 16:09
 */

import com.sky.annotation.AutoFill;
import com.sky.context.BaseContext;
import com.sky.enumeration.OperationType;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.time.LocalDateTime;

/**
 * 自动填充公共字段的切面
 */
@Slf4j
@Aspect
@Component
public class AutoFillAspect {

    /**
     * 对所有使用了@AutoFill注解的方法进行自动填充公共字段
     */
    @Before("@annotation(com.sky.annotation.AutoFill)")  // 前置通知（切入点）
    public void autoFillPublicField(JoinPoint joinPoint) throws NoSuchFieldException, IllegalAccessException {
        // 获取切入点处要增强的目标对象（传入参数）
        Object targetObj = joinPoint.getArgs()[0];
        // 获取使用切入点处方法的签名（MethodSignature）
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        // 获取该方法的 Method 对象
        Method method = signature.getMethod();
        // 获取该方法上的注解
        AutoFill autoFill = method.getAnnotation(AutoFill.class);
        // 判断该注解的value值类型
        if(autoFill.value() == OperationType.INSERT){  // 新增类型
            log.info("自动填充公共字段类型为：{}，该方法为：{}",autoFill.value(),method);
            // 获取目标对象的字节码对象
            Class<?> objClass = targetObj.getClass();
            // 反射获取目标对象的私有属性
            Field createUser = objClass.getDeclaredField("createUser");
            Field createDate = objClass.getDeclaredField("createDate");
            Field updateUser = objClass.getDeclaredField("updateUser");
            Field updateDate = objClass.getDeclaredField("updateDate");
            // 暴力反射（设置私有属性可访问）
            createUser.setAccessible(true);
            createDate.setAccessible(true);
            updateUser.setAccessible(true);
            updateDate.setAccessible(true);
            // 设置目标对象的私有属性值
            createUser.set(targetObj, BaseContext.getCurrentId());
            createDate.set(targetObj, LocalDateTime.now());
            updateUser.set(targetObj, BaseContext.getCurrentId());
            updateDate.set(targetObj, LocalDateTime.now());
        }else if ( autoFill.value() == OperationType.UPDATE ){  // 更新类型
            log.info("自动填充公共字段类型为：{}，该方法为：{}",autoFill.value(),method);
            // 获取目标对象的字节码对象
            Class<?> objClass = targetObj.getClass();
            // 反射获取目标对象的私有属性
            Field updateUser = objClass.getDeclaredField("updateUser");
            Field updateDate = objClass.getDeclaredField("updateDate");
            // 暴力反射（设置私有属性可访问）
            updateUser.setAccessible(true);
            updateDate.setAccessible(true);
            // 设置目标对象的私有属性值
            updateUser.set(targetObj, BaseContext.getCurrentId());
            updateDate.set(targetObj, LocalDateTime.now());
        }


    }

}
