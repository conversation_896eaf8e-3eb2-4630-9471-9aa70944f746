package com.sky.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sky.dto.EmployeeDTO;
import com.sky.dto.EmployeePageQueryDTO;
import com.sky.entity.Employee;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface EmployeeMapper extends BaseMapper<Employee> {

    /**
     * 根据用户名查询员工
     * @param username
     * @return
     */
    @Select("select * from employee where username = #{username}")
    Employee getByUsername(@Param("username") String username);

    List<Employee> queryPage(EmployeePageQueryDTO employeePageQueryDTO);

    /**
     * 添加员工
     * @param employee
     */
//    @Insert("insert into employee (name, username, password, phone, sex, id_number,status,create_time,update_time,create_user,update_user) values (#{employee.name},#{employee.username},#{employee.password},#{employee.phone},#{employee.sex},#{employee.idNumber},#{employee.status},#{employee.createTime},#{employee.updateTime},#{employee.createUser},#{employee.updateUser})")
//    void addEmployee(@Param("employee") Employee employee);
}
