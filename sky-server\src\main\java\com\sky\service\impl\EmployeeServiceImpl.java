package com.sky.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sky.constant.MessageConstant;
import com.sky.constant.PasswordConstant;
import com.sky.constant.StatusConstant;
import com.sky.context.BaseContext;
import com.sky.dto.EmployeeDTO;
import com.sky.dto.EmployeeLoginDTO;
import com.sky.dto.EmployeePageQueryDTO;
import com.sky.dto.PasswordEditDTO;
import com.sky.entity.Employee;
import com.sky.exception.AccountLockedException;
import com.sky.exception.AccountNotFoundException;
import com.sky.exception.PasswordErrorException;
import com.sky.interceptor.JwtTokenAdminInterceptor;
import com.sky.mapper.EmployeeMapper;
import com.sky.properties.JwtProperties;
import com.sky.result.PageResult;
import com.sky.service.EmployeeService;;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.DigestUtils;

import javax.annotation.Resource;

@Service
@Slf4j
public class EmployeeServiceImpl extends ServiceImpl<EmployeeMapper,Employee> implements EmployeeService{

    @Autowired
    private EmployeeMapper employeeMapper;
    @Autowired
    private JwtTokenAdminInterceptor jwtTokenAdminInterceptor;
    @Autowired
    private JwtProperties jwtProperties;
    @Resource(name = "passwordConstant")
    private PasswordConstant passwordConstant;
//    @Autowired
//    private HttpServletRequest request;

    /**
     * 员工登录
     *
     * @param employeeLoginDTO
     * @return
     */
    public Employee login(EmployeeLoginDTO employeeLoginDTO) {
        String username = employeeLoginDTO.getUsername();
        String password = employeeLoginDTO.getPassword();
        //1、根据用户名查询数据库中的数据
        Employee employee = employeeMapper.getByUsername(username);
        //2、处理各种异常情况（用户名不存在、密码不对、账号被锁定）
        if (employee == null) {
            //账号不存在
            throw new AccountNotFoundException(MessageConstant.ACCOUNT_NOT_FOUND);
        }
        //密码比对
        password = DigestUtils.md5DigestAsHex(password.getBytes()); //md5加密原明文密码
        if (!password.equals(employee.getPassword())) { // 将md5加密后的密码与数据库中存储的密文密码进行比对
            //密码错误
            throw new PasswordErrorException(MessageConstant.PASSWORD_ERROR);
        }
        if (employee.getStatus() == StatusConstant.DISABLE) {
            //账号被锁定
            throw new AccountLockedException(MessageConstant.ACCOUNT_LOCKED);
        }
        //3、返回实体对象
        return employee;
    }

    @Override
    public void addEmployee(EmployeeDTO employeeDTO) {
        Employee employee = new Employee();
        BeanUtils.copyProperties(employeeDTO, employee);
        employee.setStatus(StatusConstant.ENABLE);
        String defaultPassword = passwordConstant.getDefaultPassword();
        employee.setPassword(DigestUtils.md5DigestAsHex(defaultPassword.getBytes()));
        // 从请求头获取token解析出当前登录用户ID作为创建人和修改人
//        String tokenStr = request.getHeader(jwtProperties.getAdminTokenName());
//        Claims claims = JwtUtil.parseJWT(jwtProperties.getAdminSecretKey(), tokenStr);
//        String empId = claims.get(JwtClaimsConstant.EMP_ID).toString();
//        employee.setCreateUser(Long.valueOf(empId));
//        employee.setUpdateUser(Long.valueOf(empId));

        // 从ThreadLocal中获取当前登录用户ID作为创建人和修改人
        employee.setCreateUser(BaseContext.getCurrentId());
        employee.setUpdateUser(BaseContext.getCurrentId());
        log.info("新增员工，员工数据：{}", employee);
        employeeMapper.insert(employee);
//        employeeMapper.addEmployee(employee);
    }

    @Override
    public PageResult queryPage(EmployeePageQueryDTO employeePageQueryDTO) {
//        // 配置分页
//        PageHelper.startPage(employeePageQueryDTO.getPage(), employeePageQueryDTO.getPageSize());
//        // 调用mapper查询分页数据
//        List<Employee> employees = employeeMapper.queryPage(employeePageQueryDTO);
//        return new PageResult(employees.size(), employees);

        // 使用MP，创建MP分页Page对象
        Page<Employee> page = new Page<>(employeePageQueryDTO.getPage(), employeePageQueryDTO.getPageSize());
        LambdaQueryWrapper<Employee> employeeLambdaQueryWrapper = new LambdaQueryWrapper<>();
        if(StringUtils.isNotBlank(employeePageQueryDTO.getName())){
            employeeLambdaQueryWrapper.like(Employee::getName, employeePageQueryDTO.getName());
        }
        employeeMapper.selectPage(page,employeeLambdaQueryWrapper);
        return new PageResult(page.getTotal(), page.getRecords());
    }

    @Override
    public void startOrStop(Integer status, Long id) {
        Employee employee = Employee.builder()
                .id(id)
                .status(status)
                .updateUser(BaseContext.getCurrentId())
                .build();

        log.info("启用禁用员工账号：{}", employee);
        employeeMapper.updateById(employee);
    }

    @Override
    public Long updateEmployee(EmployeeDTO employeeDTO) {
        Employee employee = this.getById(employeeDTO.getId());
        BeanUtils.copyProperties(employeeDTO,employee);
        employee.setUpdateUser(BaseContext.getCurrentId());
        this.updateById(employee);
        return employee.getId();
    }

    @Override
    public Employee getInfo(Long id) {
        Employee employee = this.getById(id);
        if(employee == null){
            return null;
        }
        employee.setPassword(null);
        return employee;
    }

    @Override
    public Long updatePassword(PasswordEditDTO passwordEditDTO) {
        Employee employee = this.getOne(new LambdaQueryWrapper<Employee>()
                .eq(Employee::getId, passwordEditDTO.getEmpId())
                .eq(Employee::getPassword, DigestUtils.md5DigestAsHex(passwordEditDTO.getOldPassword().getBytes()))
        );
        if(employee == null){
            throw new PasswordErrorException("原密码错误！");
        }
        employee.setPassword(DigestUtils.md5DigestAsHex(passwordEditDTO.getNewPassword().getBytes()));
        this.updateById(employee);
        return employee.getId();
    }


}
